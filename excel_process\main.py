import os
import pandas as pd

# 输入输出文件名
input_file = "20250530.xlsx"
output_file = input_file.replace(".xlsx", "_processed.xlsx")
base_dir = os.path.dirname(os.path.abspath(__file__))
input_file_path = os.path.join(base_dir, 'files', input_file)
output_file_path = os.path.join(base_dir, 'files', output_file)

# 读取 Excel 文件
df = pd.read_excel(input_file_path)

# 确保 id 是整数并排序
df["id"] = df["id"].astype(int)
df = df.sort_values(by=["task_id", "id"])

# 填充 content 空值并转为字符串
df["content"] = df["content"].fillna("").astype(str)

# 聚合：按 task_id 分组，同时保留第一个 script_id 值
result = df.groupby("task_id").agg({
    "script_id": "first",  # 每组取第一个 script_id
    "content": lambda x: "".join(x)  # 拼接 content
}).reset_index()

# 添加 source 字段
result["source"] = "直播稿"

# 调整列顺序为 script_id, task_id, source, content
result = result[["script_id", "task_id", "source", "content"]]

# 保存结果
result.to_excel(output_file_path, index=False)
