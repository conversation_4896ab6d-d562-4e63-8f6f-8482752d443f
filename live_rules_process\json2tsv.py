import json
import csv
import os

# 文件路径配置
base_dir = os.path.dirname(os.path.abspath(__file__))
input_json_path = os.path.join(base_dir, '底线类规则20250603_02.json')
output_tsv_path = os.path.join(base_dir, 'rules_output.tsv')

# 加载 JSON
with open(input_json_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

# 写入 TSV
with open(output_tsv_path, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f, delimiter='\t')
    # 写表头
    writer.writerow(['分类', '关键词', '建议', '等级', '依据', '是否启用'])

    for category, entries in data.items():
        for entry in entries:
            for keyword, values in entry.items():
                # 补全缺失字段（旧格式可能只有2-3项）
                suggestion = values[0] if len(values) > 0 else ""
                level = values[1] if len(values) > 1 else ""
                reference = values[2] if len(values) > 2 else ""
                status = values[3] if len(values) > 3 else "启用"
                writer.writerow([category, keyword, suggestion, level, reference, status])

print(f"[✔] 已将 JSON 转换为 TSV 文件：{output_tsv_path}")
