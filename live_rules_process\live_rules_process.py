import os
import json

base_dir = os.path.dirname(os.path.abspath(__file__))

# 加载关键词列表
def load_keywords(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return set(line.strip() for line in f if line.strip())

input_file_path = '底线类规则20250530_02.json'
output_file_path = '底线类规则20250530_03.json'

# 加载原始规则 JSON
rules_path = os.path.join(base_dir, input_file_path)
with open(rules_path, 'r', encoding='utf-8') as f:
    rules = json.load(f)

# 加载各等级关键词
high_risk_rules_path = os.path.join(base_dir, 'rules', 'high.txt')
medium_risk_rules_path = os.path.join(base_dir, 'rules', 'medium.txt')
low_risk_rules_path = os.path.join(base_dir, 'rules', 'low.txt')
delete_keywords_rules_path = os.path.join(base_dir, 'rules', 'deleted.txt')

high_risk = load_keywords(high_risk_rules_path)
medium_risk = load_keywords(medium_risk_rules_path)
low_risk = load_keywords(low_risk_rules_path)
delete_keywords = load_keywords(delete_keywords_rules_path)

# 等级和建议映射
level_map = {
    '高危': (high_risk, "请删除该关键词"),
    '中危': (medium_risk, "建议结合上下文判断是否存在违规行为"),
    '低危': (low_risk, "建议修改为其他词汇")
}

# 反向索引：关键词 -> (分类名, 原始项)
keyword_index = {}
for category, items in rules.items():
    for item in items:
        for keyword in item:
            keyword_index[keyword] = (category, item)

# 先删除关键词
for keyword in delete_keywords:
    if keyword in keyword_index:
        cat, item = keyword_index[keyword]
        if keyword in item:
            rules[cat].remove(item)

# 添加或更新关键词
for level, (keywords, suggestion) in level_map.items():
    for keyword in keywords:
        if keyword in delete_keywords:
            continue  # 忽略被删除的关键词

        if keyword in keyword_index:
            # 更新等级和建议，保留已有的依据字段或补空
            cat, item = keyword_index[keyword]
            basis = item[keyword][2] if len(item[keyword]) > 2 else ""
            item[keyword] = [suggestion, level, basis]
        else:
            # 添加到“个人信息类”，初始化封禁依据为空
            new_entry = {keyword: [suggestion, level, ""]}
            if "个人信息类" not in rules:
                rules["个人信息类"] = []
            rules["个人信息类"].append(new_entry)

# 保存修改后的 JSON
output_file_path = os.path.join(base_dir, output_file_path)
with open(output_file_path, 'w', encoding='utf-8') as f:
    json.dump(rules, f, ensure_ascii=False, indent=2)
