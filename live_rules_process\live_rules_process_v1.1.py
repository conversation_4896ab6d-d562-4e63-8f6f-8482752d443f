import os
import json
from collections import defaultdict

# === 常量定义 ===
DEFAULT_CATEGORY = "个人信息类"
DELETE_CATEGORY = "底线类违规_其他"

# === 工具函数 ===
def load_keywords(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        return set(line.strip() for line in f if line.strip())

def normalize_entry(entry):
    """
    确保每个关键词对应的值为3项：[建议, 等级, 依据]
    """
    for keyword, value in entry.items():
        if len(value) == 2:
            value.append("")
        elif len(value) != 3:
            raise ValueError(f"格式错误，关键词 {keyword} 的值应为长度为 2 或 3 的列表：{value}")

def build_index_and_apply_deletions(rules, delete_keywords):
    """
    同时构建反向索引并删除需要排除的关键词
    """
    keyword_index = {}
    for category in list(rules.keys()):
        new_items = []
        for item in rules[category]:
            normalize_entry(item)
            keyword = next(iter(item))
            if keyword in delete_keywords:
                print(f"[删除] 已移除关键词：{keyword}")
                continue
            keyword_index[keyword] = (category, item)
            new_items.append(item)
        rules[category] = new_items
    return keyword_index

def update_or_add_keywords(rules, keyword_index, level_map, delete_keywords):
    """
    根据等级关键词，更新或添加新项
    """
    for level, (keywords, suggestion) in level_map.items():
        for keyword in keywords:
            if keyword in delete_keywords:
                continue

            if keyword in keyword_index:
                cat, item = keyword_index[keyword]
                item[keyword] = [suggestion, level, item[keyword][2] if len(item[keyword]) > 2 else ""]
                print(f"[更新] {keyword} -> {level}")
            else:
                new_entry = {keyword: [suggestion, level, ""]}
                rules[DEFAULT_CATEGORY].append(new_entry)
                print(f"[新增] {keyword} -> {level}")

def save_rules(rules, output_path):
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(rules, f, ensure_ascii=False, indent=2)

# === 主程序入口 ===
def main():
    base_dir = os.path.dirname(os.path.abspath(__file__))
    input_file_path = os.path.join(base_dir, '底线类规则20250530_03.json')
    output_file_path = os.path.join(base_dir, '底线类规则20250530_04.json')

    # 加载规则 JSON
    with open(input_file_path, 'r', encoding='utf-8') as f:
        rules_raw = json.load(f)

    # 使用 defaultdict 简化分类添加逻辑
    rules = defaultdict(list, rules_raw)

    # 加载关键词文件
    rules_dir = os.path.join(base_dir, 'rules')
    high_risk = load_keywords(os.path.join(rules_dir, 'high.txt'))
    medium_risk = load_keywords(os.path.join(rules_dir, 'medium.txt'))
    low_risk = load_keywords(os.path.join(rules_dir, 'low.txt'))
    delete_keywords = load_keywords(os.path.join(rules_dir, 'deleted.txt'))

    # 建议和等级映射表
    level_map = {
        '高危': (high_risk, "请删除该关键词"),
        '中危': (medium_risk, "建议结合上下文判断是否存在违规行为"),
        '低危': (low_risk, "建议修改为其他词汇"),
    }

    # 构建索引并清理被删除项
    keyword_index = build_index_and_apply_deletions(rules, delete_keywords)

    # 处理更新和添加
    update_or_add_keywords(rules, keyword_index, level_map, delete_keywords)

    # 保存结果
    save_rules(rules, output_file_path)
    print(f"\n✅ 已保存结果至：{output_file_path}")

if __name__ == "__main__":
    main()
