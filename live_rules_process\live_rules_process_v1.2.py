import os
import csv
import json
from collections import defaultdict

base_dir = os.path.dirname(os.path.abspath(__file__))

# 文件路径
history_json_path = os.path.join(base_dir, '底线类规则20250603_02.json')
input_tsv_path = os.path.join(base_dir, "rules",'rules_output.tsv')
deleted_txt_path = os.path.join(base_dir, "rules",'deleted.txt')
output_json_path = os.path.join(base_dir, '底线类规则20250604_01.json')


def load_deleted_keywords(filepath):
    if not os.path.exists(filepath):
        return set()
    with open(filepath, 'r', encoding='utf-8') as f:
        return set(line.strip() for line in f if line.strip())


def load_rules_from_tsv(filepath):
    rules = defaultdict(list)
    with open(filepath, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter='\t')
        for row in reader:
            category = row.get('分类', '').strip()
            keyword = row.get('关键词', '').strip()
            level = row.get('等级', '').strip()
            suggestion = row.get('建议', '').strip()
            source = row.get('依据', '').strip()
            status = row.get('是否启用', '启用').strip()

            if not category or not keyword or not level or not suggestion:
                print(f"[跳过] TSV无效行：{row}")
                continue
            if status != '启用':
                continue
            rules[category].append({
                keyword: [suggestion, level, source, status]
            })
    return rules


def load_history_json(filepath):
    if not os.path.exists(filepath):
        return defaultdict(list)
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
    # 兼容旧格式，扩展每条到4元素（建议，等级，依据，状态）
    fixed = defaultdict(list)
    for category, entries in data.items():
        for entry in entries:
            for keyword, values in entry.items():
                # values可能只有2个元素，补齐
                while len(values) < 4:
                    if len(values) == 2:
                        values.append("")   # 依据空
                    elif len(values) == 3:
                        values.append("启用")  # 默认启用
                fixed[category].append({keyword: values})
    return fixed


def merge_rules(history_rules, new_rules, deleted_keywords):
    # 把新规则加入历史，覆盖关键词。先做关键词索引，方便覆盖
    merged = defaultdict(list)

    # 建立索引： category -> keyword -> entry
    index = {}
    for category, entries in history_rules.items():
        index.setdefault(category, {})
        for entry in entries:
            keyword = next(iter(entry.keys()))
            index[category][keyword] = entry

    # 更新索引：用新规则覆盖或新增
    for category, entries in new_rules.items():
        index.setdefault(category, {})
        for entry in entries:
            keyword = next(iter(entry.keys()))
            index[category][keyword] = entry

    # 删除被删除关键词
    for category in list(index.keys()):
        for keyword in list(index[category].keys()):
            if keyword in deleted_keywords:
                del index[category][keyword]
        if not index[category]:
            del index[category]

    # 重组格式
    for category, kw_dict in index.items():
        merged[category] = list(kw_dict.values())

    return merged


def save_rules_to_json(rules, filepath):
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(rules, f, ensure_ascii=False, indent=2)


def main():
    deleted_keywords = load_deleted_keywords(deleted_txt_path)
    history_rules = load_history_json(history_json_path)
    new_rules = load_rules_from_tsv(input_tsv_path)

    merged = merge_rules(history_rules, new_rules, deleted_keywords)

    save_rules_to_json(merged, output_json_path)
    print(f"合并完成，输出文件：{output_json_path}")


if __name__ == '__main__':
    main()
