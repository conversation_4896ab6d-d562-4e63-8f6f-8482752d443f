import time
import requests
from datetime import datetime, timedelta

tokenId = "0271c87a-1d72-4bf3-8ada-f5c2e36958a9"
#获取指定时间内的数据
# date = "2025-05-30"
# 获取当前日期
today = datetime.today()
yesterday = today - timedelta(days=1)
# 格式化为 YYYY-MM-DD 格式的字符串
date = yesterday.strftime('%Y-%m-%d')
print(date)

# 固定参数
url = f"https://ark-rds.bitautotech.com/api/v1/console/applyExportExcel?tokenId={tokenId}"
headers = {
    "Content-Type": "application/json;charset=UTF-8",
    "Origin": "https://ark.bitautotech.com",
    "Referer": "https://ark.bitautotech.com/rdscluster/sql-console/9943/%E6%98%93%E5%88%9B%E7%9B%B4%E6%92%AD?dbName=vlive_prod",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36",
    "Accept": "application/json, text/plain, */*"
}

# 请求体固定部分
cluster_id = "9943"
db_name = "vlive_prod"

# 日期和时间段设置
time_ranges = [
    ("00:00:00", "10:00:00"),
    ("10:00:00", "14:00:00"),
    ("14:00:00", "18:00:00"),
    ("18:00:00", "00:00:00")
]

for start_time, end_time in time_ranges:
    start_datetime = f"{date} {start_time}"
    # 如果结束时间是 00:00:00，表示到第二天
    if end_time == "00:00:00":
        next_day = (datetime.strptime(date, "%Y-%m-%d") + timedelta(days=1)).strftime("%Y-%m-%d")
        end_datetime = f"{next_day} 00:00:00"
    else:
        end_datetime = f"{date} {end_time}"

    sql = (
        f"select id,create_time,script_id,task_id,stage_name,scene_name,label_name,content "
        f"from live_script_content where create_time >= '{start_datetime}' and create_time < '{end_datetime}'"
    )

    data = {
        "clusterId": cluster_id,
        "dbName": db_name,
        "sqlString": sql
    }

    print(f"Sending request for time range: {start_datetime} - {end_datetime}")
    response = requests.post(url, headers=headers, json=data)
    time.sleep(3)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}\n")



'''
SELECT
    MIN(hour_start) AS segment_start,
    MAX(hour_end) AS segment_end,
    SUM(cnt) AS total_rows
FROM (
    SELECT 
        hour_block AS hour_start,
        DATE_ADD(hour_block, INTERVAL 1 HOUR) AS hour_end,
        cnt,
        @sum := IF(@sum + cnt > 30000, cnt, @sum + cnt) AS running_sum,
        @grp := IF(@sum + cnt > 30000, @grp + 1, @grp) AS group_id
    FROM (
        SELECT 
            DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') AS hour_block,
            COUNT(*) AS cnt
        FROM live_script_content
        WHERE create_time >= '2025-06-02 00:00:00' AND create_time < '2025-06-03 00:00:00'
        GROUP BY hour_block
        ORDER BY hour_block
    ) AS hourly_data,
    (SELECT @sum := 0, @grp := 1) AS vars
) AS grouped_data
GROUP BY group_id
ORDER BY segment_start;


select id,create_time,script_id,task_id,stage_name,scene_name,label_name,content from live_script_content where create_time >= '2025-05-30 20:00:00' and create_time < '2025-05-31 00:00:00'

'''