import os
import pandas as pd
import requests
from requests.exceptions import RequestException
import traceback

# 输入输出文件名
input_file = "20250527.xlsx"
output_file = input_file.replace(".xlsx", "_result1111.xlsx")
base_dir = os.path.dirname(os.path.abspath(__file__))
input_file_path = os.path.join(base_dir, 'files', input_file)
output_file_path = os.path.join(base_dir, 'files', output_file)


# api_url = "http://grc-grc-test.td-test2.bitautotech.com/api/v1/analyze"
api_url = "http://10.168.17.76:8000/api/v1/analyze"
# 初始化输出结果列表
results_data = []

try:
    # 读取 Excel 文件
    df = pd.read_excel(input_file_path)
    MAX_RETRIES = 3
    total = len(df)

    for index, row in enumerate(df.itertuples(), start=1):
        content = row.content
        uid = str(row.id)
        source = row.source

        print(f"[信息] 正在处理 处理进度 {index}/{total}", end="\r")

        success = False
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                response = requests.post(api_url, json={"content": content, "uid": uid}, timeout=20)
                response.raise_for_status()
                json_data = response.json()

                if json_data.get("code") == 0 and "results" in json_data.get("data", {}):
                    for result in json_data["data"]["results"]:
                        results_data.append({
                            "id": uid,
                            "source": source,
                            "keyword": result.get("keyword", ""),
                            "context": result.get("context", ""),
                            "suggestion": result.get("suggestion", ""),
                            "severity": result.get("severity", ""),
                            "rule_name": result.get("rule_name", "")
                        })
                    success = True
                    break
                else:
                    print(f"\n[警告] 接口响应内容异常，uid: {uid}")
                    break
            except Exception as e:
                print(f"\n[重试] 第 {attempt} 次请求失败，uid: {uid}，错误信息: {e}")

        if not success:
            print(f"\n[失败] 已重试 {MAX_RETRIES} 次仍失败，跳过 uid: {uid}")



    # 尝试写入结果到新 Excel 文件
    try:
        output_df = pd.DataFrame(results_data)
        output_df.to_excel(output_file_path, index=False)
        print(f"[成功] 结果已保存至 {output_file_path}")
    except Exception as e:
        print(f"[错误] 写入结果文件失败: {str(e)}")

except Exception as e:
    print(f"[错误] 读取输入文件失败: {str(e)}")
    traceback.print_exc()
